{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}{{ ledger.name }} Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-6 breadcrumb-animation">
    <div class="flex items-center gap-4 text-sm text-[#40657F]">
      <a href="{% url 'finances:budget_ledger_management' %}" class="hover:text-[#7AB2D3] transition-colors duration-200">
        <i class="fas fa-chart-line mr-2"></i>Budget & Ledger Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ ledger.name }}</span>
    </div>
  </div>

  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-book text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            {{ ledger.name }}
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Ledger Account Details & Transaction History
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'finances:edit_ledger' ledger.pk %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-edit group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Edit Ledger</span>
        </a>
        <a
          href="{% url 'finances:budget_ledger_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group border border-[#B9D8EB]"
        >
          <i
            class="fas fa-arrow-left group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Back to Management</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Account Information Section -->
  <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 stats-cards-fade-in">
    <!-- Account Code -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-hashtag text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Account Code</h3>
          <p class="text-[#40657F] font-mono font-bold text-xl">{{ ledger.code|default:"Not Set" }}</p>
        </div>
      </div>
    </div>

    <!-- Account Type -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-layer-group text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Account Type</h3>
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold
            {% if ledger.ledger_type == 'Asset' %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30
            {% elif ledger.ledger_type == 'Revenue' %}bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30
            {% elif ledger.ledger_type == 'Expense' %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30
            {% else %}bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30{% endif %}"
          >
            {{ ledger.ledger_type }}
          </span>
        </div>
      </div>
    </div>

    <!-- Current Balance -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
          <i class="fas fa-dollar-sign text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Actual Amount</h3>
          <p class="text-[#40657F] font-mono font-bold text-xl">MWK {{ total_amount|intcomma }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Budget Information Section -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 budget-cards-fade-in">
    <!-- Approved Budget -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-clipboard-list text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Approved Budget</h3>
          <p class="text-[#74C69D] font-mono font-bold text-xl">MWK {{ budget_amount|intcomma }}</p>
          {% if budget_line %}
          <p class="text-xs text-[#40657F] mt-1">{{ active_term.term_name }} - {{ active_term.academic_year.name }}</p>
          {% else %}
          <p class="text-xs text-[#B9D8EB] mt-1">No budget allocated</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Budget Variance -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-balance-scale text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Budget Variance</h3>
          <p class="font-mono font-bold text-xl
            {% if budget_variance > 0 %}text-[#74C69D]{% elif budget_variance < 0 %}text-[#F28C8C]{% else %}text-[#40657F]{% endif %}">
            {% if budget_variance > 0 %}+{% endif %}MWK {{ budget_variance|intcomma }}
          </p>
          <p class="text-xs mt-1
            {% if budget_variance > 0 %}text-[#74C69D]{% elif budget_variance < 0 %}text-[#F28C8C]{% else %}text-[#40657F]{% endif %}">
            {% if budget_variance > 0 %}Under Budget{% elif budget_variance < 0 %}Over Budget{% else %}On Budget{% endif %}
          </p>
        </div>
      </div>
    </div>

    <!-- Budget Utilization -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#40657F]/5 to-[#2C3E50]/5 border-l-4 border-[#40657F]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#40657F] rounded-xl flex items-center justify-center relative">
          <i class="fas fa-percentage text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Budget Utilization</h3>
          <div class="flex items-center gap-3">
            <p class="font-mono font-bold text-xl
              {% if utilization_percentage <= 80 %}text-[#74C69D]{% elif utilization_percentage <= 100 %}text-[#7AB2D3]{% else %}text-[#F28C8C]{% endif %}">
              {{ utilization_percentage|floatformat:1 }}%
            </p>
            <!-- Mini Progress Bar -->
            <div class="flex-1 bg-[#E2F1F9] rounded-full h-2">
              <div class="h-2 rounded-full transition-all duration-500
                {% if utilization_percentage <= 80 %}bg-[#74C69D]{% elif utilization_percentage <= 100 %}bg-[#7AB2D3]{% else %}bg-[#F28C8C]{% endif %}"
                style="width: {{ utilization_percentage|floatformat:0 }}%"></div>
            </div>
          </div>
          <p class="text-xs text-[#40657F] mt-1">
            {% if utilization_percentage <= 80 %}Good utilization{% elif utilization_percentage <= 100 %}Near budget limit{% else %}Over budget{% endif %}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Transactions Section -->
  <div class="card-modern p-8 transactions-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-history text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Recent Transactions
        </h3>
        <p class="text-[#40657F] text-sm">
          Last 20 transactions for this account
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-list text-sm"></i>
          <span>{{ recent_transactions.count }} transaction{{ recent_transactions.count|pluralize }}</span>
        </div>
      </div>
    </div>

    <!-- Transactions Table -->
    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-calendar text-[#7AB2D3]"></i>
                Date
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-file-alt text-[#40657F]"></i>
                Journal Entry
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-comment text-[#74C69D]"></i>
                Description
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-exchange-alt text-[#F28C8C]"></i>
                Type
              </div>
            </th>
            <th
              class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-dollar-sign text-[#7AB2D3]"></i>
                Amount
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for transaction in recent_transactions %}
          <tr
            class="transaction-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <span class="text-[#2C3E50] font-medium">{{ transaction.journal_entry.date }}</span>
            </td>
            <td class="px-6 py-4">
              <div class="flex flex-col">
                <span class="font-mono text-[#40657F] font-bold">{{ transaction.journal_entry.journal_number }}</span>
                {% if transaction.journal_entry.voucher %}
                <span class="text-xs text-[#7AB2D3]">Voucher: {{ transaction.journal_entry.voucher }}</span>
                {% endif %}
              </div>
            </td>
            <td class="px-6 py-4">
              <span class="text-[#2C3E50]">
                {{ transaction.description|default:transaction.journal_entry.description|default:"No description" }}
              </span>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold
                {% if transaction.line_type == 'Debit' %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30
                {% else %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30{% endif %}"
              >
                {{ transaction.line_type }}
              </span>
            </td>
            <td class="px-6 py-4 text-right">
              <span class="font-mono text-[#2C3E50] font-bold">
                MWK {{ transaction.amount|intcomma }}
              </span>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="5" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-history text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Transactions Found
                  </h3>
                  <p class="text-[#40657F]">
                    This account has no transaction history yet.
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-20px);
    animation: breadcrumbSlideDown 0.6s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Stats Cards Animation */
  .stats-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statsCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Budget Cards Animation */
  .budget-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: budgetCardsFadeIn 0.8s ease-out 1.2s forwards;
  }

  /* Transactions Section Animation */
  .transactions-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: transactionsSectionFadeIn 0.8s ease-out 1.4s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .transaction-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: transactionRowSlideIn 0.4s ease-out forwards;
  }

  .transaction-row:nth-child(1) { animation-delay: 1.6s; }
  .transaction-row:nth-child(2) { animation-delay: 1.7s; }
  .transaction-row:nth-child(3) { animation-delay: 1.8s; }
  .transaction-row:nth-child(4) { animation-delay: 1.9s; }
  .transaction-row:nth-child(5) { animation-delay: 2s; }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes statsCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes budgetCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes transactionsSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes transactionRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .transaction-row {
      animation-delay: 1.2s;
    }

    .transaction-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--item-index, 1));
    }
  }
</style>

{% endblock %}
