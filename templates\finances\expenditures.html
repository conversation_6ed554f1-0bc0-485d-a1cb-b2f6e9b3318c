{% extends 'base.html' %} {% load humanize %} {% block title %}Finance Overview
| {% endblock%} {% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-chart-line text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            Finance Overview
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Track income, expenses and balance
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'finances:add_expenditure'%}"
          class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group text-center"
        >
          <i
            class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"
          ></i>
          Create Payment
        </a>
        <a
          href="{% url 'finances:reversal_list' %}"
          class="bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group text-center"
        >
          <i
            class="fas fa-undo mr-2 group-hover:rotate-180 transition-transform duration-300"
          ></i>
          View Reversals
        </a>
        <button
          class="bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-download mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Export Report
        </button>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-[#B9D8EB]"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Finances</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Overview</span>
    </nav>
  </div>
  <!-- Finance Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
    <!-- Total Income Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 border-l-4 border-[#74C69D] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-arrow-up text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#74C69D] font-semibold">Active</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Total Income</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#74C69D] mb-2 font-display block"
          >MWK {{ total_collected | intcomma}}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Amount collected this period</span
        >
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#74C69D] text-xs font-semibold"
        >
          <i class="fas fa-trending-up"></i>
          <span>+12.5%</span>
        </div>
        <span class="text-[#40657F] text-xs">vs last month</span>
      </div>
    </div>

    <!-- Total Spent Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 border-l-4 border-[#F28C8C] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-arrow-down text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#F28C8C] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#F28C8C] font-semibold">Tracking</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Total Spent</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#F28C8C] mb-2 font-display block"
          >MWK {{ total_used | intcomma }}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Amount spent this period</span
        >
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#F28C8C] text-xs font-semibold"
        >
          <i class="fas fa-trending-down"></i>
          <span>-8.3%</span>
        </div>
        <span class="text-[#40657F] text-xs">vs last month</span>
      </div>
    </div>

    <!-- Available Balance Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 border-l-4 border-[#7AB2D3] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-wallet text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#7AB2D3] font-semibold">Available</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Available Balance</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#7AB2D3] mb-2 font-display block"
          >MWK {{total_balance | intcomma }}</span
        >
        <span class="text-sm text-[#40657F] font-medium">Amount remaining</span>
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#7AB2D3] text-xs font-semibold"
        >
          <i class="fas fa-chart-line"></i>
          <span>Stable</span>
        </div>
        <span class="text-[#40657F] text-xs">healthy balance</span>
      </div>
    </div>
  </div>
  <!-- Payment History Table -->
  <div class="card-modern p-6">
    <div class="flex flex-col md:flex-row items-center gap-4 justify-between mb-6">
      <div class="flex items-center gap-3 w-full md:w-fit">
        <div
          class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg"
        >
          <i class="fas fa-history text-white text-xs sm:text-sm"></i>
        </div>
        <div>
          <h2 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
            Payment History
          </h2>
          <p class="text-[#40657F] text-sm">Recent financial transactions</p>
        </div>
      </div>
      <!-- Search and Filter -->
      <div class="flex flex-col lg:flex-row items-center gap-4 w-full lg:w-fit">
        <!-- Search Form -->
        <form method="GET" class="flex flex-col md:flex-row items-center gap-3 w-full">
          <!-- Search Input -->
          <div class="relative w-full md:w-64">
            <input
              type="text"
              name="search"
              value="{{ search_query }}"
              placeholder="Search by ID, description, payee..."
              class="pl-10 pr-4 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm w-full text-[#2C3E50]"
            />
            <i
              class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
            ></i>
          </div>

          <!-- Advanced Filters Toggle -->
          <button
            type="button"
            id="toggleFilters"
            class="bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-semibold py-2 px-4 rounded-lg hover:from-[#2C3E50] hover:to-[#40657F] transition-all duration-300 text-sm whitespace-nowrap"
          >
            <i class="fas fa-filter mr-2"></i>
            Advanced Filters
          </button>

          <!-- Search Button -->
          <button
            type="submit"
            class="bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-semibold py-2 px-4 rounded-lg hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 text-sm whitespace-nowrap"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>

          <!-- Clear Filters -->
          {% if search_query or date_from or date_to or payee_filter or amount_min or amount_max %}
          <a
            href="{% url 'finances:expenditures' %}"
            class="bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-semibold py-2 px-4 rounded-lg hover:from-[#e07575] hover:to-[#F28C8C] transition-all duration-300 text-sm whitespace-nowrap"
          >
            <i class="fas fa-times mr-2"></i>
            Clear
          </a>
          {% endif %}
        </form>
      </div>
    </div>

    <!-- Advanced Filters Panel -->
    <div id="filtersPanel" class="hidden bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] p-6 rounded-lg mb-6 border border-[#B9D8EB]">
      <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Preserve search query -->
        <input type="hidden" name="search" value="{{ search_query }}">

        <!-- Date Range -->
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Date From</label>
          <input
            type="date"
            name="date_from"
            value="{{ date_from }}"
            class="w-full px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-[#2C3E50]"
          />
        </div>

        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Date To</label>
          <input
            type="date"
            name="date_to"
            value="{{ date_to }}"
            class="w-full px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-[#2C3E50]"
          />
        </div>

        <!-- Payee Filter -->
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Payee</label>
          <select
            name="payee"
            class="w-full px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-[#2C3E50]"
          >
            <option value="">All Payees</option>
            {% for payee in unique_payees %}
            <option value="{{ payee }}" {% if payee_filter == payee %}selected{% endif %}>{{ payee }}</option>
            {% endfor %}
          </select>
        </div>

        <!-- Amount Range -->
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Min Amount (MWK)</label>
          <input
            type="number"
            name="amount_min"
            value="{{ amount_min }}"
            placeholder="0"
            min="0"
            step="0.01"
            class="w-full px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-[#2C3E50]"
          />
        </div>

        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Max Amount (MWK)</label>
          <input
            type="number"
            name="amount_max"
            value="{{ amount_max }}"
            placeholder="1000000"
            min="0"
            step="0.01"
            class="w-full px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-[#2C3E50]"
          />
        </div>

        <!-- Filter Actions -->
        <div class="flex items-end gap-2">
          <button
            type="submit"
            class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-semibold py-2 px-4 rounded-lg hover:from-[#5fb085] hover:to-[#74C69D] transition-all duration-300 text-sm flex-1"
          >
            <i class="fas fa-filter mr-2"></i>
            Apply Filters
          </button>
        </div>
      </form>
    </div>

    <!-- Results Summary -->
    {% if expenditure_list %}
    <div class="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6 p-4 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-lg border border-[#B9D8EB]">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <div class="w-3 h-3 bg-[#7AB2D3] rounded-full"></div>
          <span class="text-sm font-semibold text-[#2C3E50]">
            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ total_count }} expenditures
          </span>
        </div>
        {% if search_query or date_from or date_to or payee_filter or amount_min or amount_max %}
        <div class="flex items-center gap-2">
          <i class="fas fa-filter text-[#40657F] text-sm"></i>
          <span class="text-sm text-[#40657F]">Filtered results</span>
        </div>
        {% endif %}
      </div>

      <!-- Quick Stats -->
      <div class="flex items-center gap-4 text-sm">
        <div class="flex items-center gap-2">
          <i class="fas fa-list text-[#7AB2D3]"></i>
          <span class="text-[#2C3E50] font-medium">{{ total_count }} Total</span>
        </div>
        <div class="flex items-center gap-2">
          <i class="fas fa-calendar text-[#74C69D]"></i>
          <span class="text-[#2C3E50] font-medium">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
        </div>
      </div>
    </div>

    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr
              class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] border-b border-[#B9D8EB]"
            >
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Document No.
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Description
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Term
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Date
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Amount
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB] text-nowrap">
            {% for expense in expenditure_list %}
            <tr
              class="hover:bg-gradient-to-r hover:from-[#E2F1F9]/50 hover:to-[#B9D8EB]/30 transition-all duration-200 group"
            >
              <td class="py-4 px-6">
                <a
                  href="{% url 'finances:expenditure_detail' expense.slug %}"
                  class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-semibold transition-colors duration-300"
                >
                  <div
                    class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <span class="text-white text-xs font-bold"
                      >{{ expense.outflow_id|slice:":2" }}</span
                    >
                  </div>
                  {{expense.outflow_id}}
                </a>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center shadow-sm"
                  >
                    <i class="fas fa-receipt text-white text-sm"></i>
                  </div>
                  <div>
                    <div class="font-medium text-[#2C3E50] capitalize">
                      {{expense.description}}
                    </div>
                    <div class="text-xs text-[#40657F]">Expenditure</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#E2F1F9] text-[#40657F]"
                >
                  {{expense.term.term_name}}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="text-[#2C3E50] font-medium">{{expense.date}}</div>
                <div class="text-xs text-[#40657F]">Transaction date</div>
              </td>
              <td class="py-4 px-6">
                <div class="text-xl font-bold text-[#F28C8C]">
                  MWK {{ expense.total_amount | intcomma}}
                </div>
                <div class="text-xs text-[#40657F]">Amount spent</div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  <a
                    href="{% url 'finances:expenditure_detail' expense.slug %}"
                    class="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white text-xs font-medium rounded-lg hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-eye"></i>
                    View
                  </a>
                  <button
                    class="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white text-xs font-medium rounded-lg hover:from-[#2C3E50] hover:to-[#40657F] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-edit"></i>
                    Edit
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination Controls -->
    {% if page_obj.has_other_pages %}
    <div class="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 p-6 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-lg border border-[#B9D8EB]">
      <!-- Page Info -->
      <div class="flex items-center gap-2">
        <span class="text-sm text-[#2C3E50] font-medium">
          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>
        <div class="w-1 h-1 bg-[#B9D8EB] rounded-full"></div>
        <span class="text-sm text-[#40657F]">
          {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ total_count }} results
        </span>
      </div>

      <!-- Pagination Links -->
      <div class="flex items-center gap-2">
        <!-- First Page -->
        {% if page_obj.has_previous %}
        <a
          href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if payee_filter %}payee={{ payee_filter }}&{% endif %}{% if amount_min %}amount_min={{ amount_min }}&{% endif %}{% if amount_max %}amount_max={{ amount_max }}&{% endif %}page=1"
          class="inline-flex items-center justify-center w-10 h-10 bg-white border border-[#B9D8EB] rounded-lg hover:bg-[#E2F1F9] hover:border-[#7AB2D3] transition-all duration-300 text-[#40657F] hover:text-[#2C3E50]"
          title="First page"
        >
          <i class="fas fa-angle-double-left text-sm"></i>
        </a>

        <!-- Previous Page -->
        <a
          href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if payee_filter %}payee={{ payee_filter }}&{% endif %}{% if amount_min %}amount_min={{ amount_min }}&{% endif %}{% if amount_max %}amount_max={{ amount_max }}&{% endif %}page={{ page_obj.previous_page_number }}"
          class="inline-flex items-center justify-center w-10 h-10 bg-white border border-[#B9D8EB] rounded-lg hover:bg-[#E2F1F9] hover:border-[#7AB2D3] transition-all duration-300 text-[#40657F] hover:text-[#2C3E50]"
          title="Previous page"
        >
          <i class="fas fa-angle-left text-sm"></i>
        </a>
        {% else %}
        <span class="inline-flex items-center justify-center w-10 h-10 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#B9D8EB] cursor-not-allowed">
          <i class="fas fa-angle-double-left text-sm"></i>
        </span>
        <span class="inline-flex items-center justify-center w-10 h-10 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#B9D8EB] cursor-not-allowed">
          <i class="fas fa-angle-left text-sm"></i>
        </span>
        {% endif %}

        <!-- Page Numbers -->
        {% for num in page_obj.paginator.page_range %}
          {% if page_obj.number == num %}
          <span class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-semibold rounded-lg shadow-md">
            {{ num }}
          </span>
          {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <a
            href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if payee_filter %}payee={{ payee_filter }}&{% endif %}{% if amount_min %}amount_min={{ amount_min }}&{% endif %}{% if amount_max %}amount_max={{ amount_max }}&{% endif %}page={{ num }}"
            class="inline-flex items-center justify-center w-10 h-10 bg-white border border-[#B9D8EB] rounded-lg hover:bg-[#E2F1F9] hover:border-[#7AB2D3] transition-all duration-300 text-[#40657F] hover:text-[#2C3E50] font-medium"
          >
            {{ num }}
          </a>
          {% endif %}
        {% endfor %}

        <!-- Next Page -->
        {% if page_obj.has_next %}
        <a
          href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if payee_filter %}payee={{ payee_filter }}&{% endif %}{% if amount_min %}amount_min={{ amount_min }}&{% endif %}{% if amount_max %}amount_max={{ amount_max }}&{% endif %}page={{ page_obj.next_page_number }}"
          class="inline-flex items-center justify-center w-10 h-10 bg-white border border-[#B9D8EB] rounded-lg hover:bg-[#E2F1F9] hover:border-[#7AB2D3] transition-all duration-300 text-[#40657F] hover:text-[#2C3E50]"
          title="Next page"
        >
          <i class="fas fa-angle-right text-sm"></i>
        </a>

        <!-- Last Page -->
        <a
          href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if payee_filter %}payee={{ payee_filter }}&{% endif %}{% if amount_min %}amount_min={{ amount_min }}&{% endif %}{% if amount_max %}amount_max={{ amount_max }}&{% endif %}page={{ page_obj.paginator.num_pages }}"
          class="inline-flex items-center justify-center w-10 h-10 bg-white border border-[#B9D8EB] rounded-lg hover:bg-[#E2F1F9] hover:border-[#7AB2D3] transition-all duration-300 text-[#40657F] hover:text-[#2C3E50]"
          title="Last page"
        >
          <i class="fas fa-angle-double-right text-sm"></i>
        </a>
        {% else %}
        <span class="inline-flex items-center justify-center w-10 h-10 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#B9D8EB] cursor-not-allowed">
          <i class="fas fa-angle-right text-sm"></i>
        </span>
        <span class="inline-flex items-center justify-center w-10 h-10 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#B9D8EB] cursor-not-allowed">
          <i class="fas fa-angle-double-right text-sm"></i>
        </span>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9] rounded-full flex items-center justify-center"
        >
          <i class="fas fa-receipt text-[#40657F] text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-[#2C3E50] mb-2">
            No Expenditures Found
          </h3>
          <p class="text-[#40657F] text-lg">
            No financial transactions have been recorded yet.
          </p>
          <p class="text-[#40657F] text-sm mt-2">
            Start by creating your first payment record.
          </p>
        </div>
        <a
          href="{% url 'finances:add_expenditure'%}"
          class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-plus mr-2"></i>
          Create First Payment
        </a>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Quick Stats Summary -->
  {% if expenditure_list %}
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <div class="card-modern p-6 text-center border-l-4 border-[#7AB2D3]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-list text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Total Transactions
      </h3>
      <p class="text-3xl font-bold text-[#7AB2D3]">
        {{ expenditure_list|length }}
      </p>
      <p class="text-[#40657F] text-sm">This period</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#74C69D]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-calendar text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        This Month
      </h3>
      <p class="text-3xl font-bold text-[#74C69D]">
        {{ expenditure_list|length }}
      </p>
      <p class="text-[#40657F] text-sm">Recent activity</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#F28C8C]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-chart-bar text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Average Payment
      </h3>
      <p class="text-lg font-bold text-[#F28C8C]">MWK 25,000</p>
      <p class="text-[#40657F] text-sm">Per transaction</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#40657F]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-trending-up text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Growth Rate
      </h3>
      <p class="text-lg font-bold text-[#40657F]">+15.2%</p>
      <p class="text-[#40657F] text-sm">vs last period</p>
    </div>
  </div>
  {% endif %}
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('toggleFilters');
    const filtersPanel = document.getElementById('filtersPanel');

    if (toggleButton && filtersPanel) {
        // Check if any filters are active by checking URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const hasActiveFilters = urlParams.has('date_from') || urlParams.has('date_to') ||
                                urlParams.has('payee') || urlParams.has('amount_min') ||
                                urlParams.has('amount_max');

        if (hasActiveFilters) {
            filtersPanel.classList.remove('hidden');
            toggleButton.innerHTML = '<i class="fas fa-filter mr-2"></i>Hide Filters';
        }

        toggleButton.addEventListener('click', function() {
            filtersPanel.classList.toggle('hidden');

            if (filtersPanel.classList.contains('hidden')) {
                toggleButton.innerHTML = '<i class="fas fa-filter mr-2"></i>Advanced Filters';
            } else {
                toggleButton.innerHTML = '<i class="fas fa-filter mr-2"></i>Hide Filters';
            }
        });
    }

    // Auto-submit search form on Enter key
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.closest('form').submit();
            }
        });
    }
});
</script>
{% endblock %}
