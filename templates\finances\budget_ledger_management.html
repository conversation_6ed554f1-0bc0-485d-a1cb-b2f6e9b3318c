{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}Budget & Ledger Management | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-line text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Budget & Ledger Management
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage chart of accounts, budgets, and financial planning
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'finances:add_ledger' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Ledger</span>
        </a>
        <a
          href="{% url 'finances:add_budget' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-calculator group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Add Budget</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Statistics Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 stats-cards-fade-in">
    <!-- Total Ledgers -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-book text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Ledgers</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ ledger_stats.total_ledgers }}</p>
        </div>
      </div>
    </div>

    <!-- Assets -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-coins text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Assets</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ ledger_stats.asset_count }}</p>
        </div>
      </div>
    </div>

    <!-- Revenue Accounts -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
          <i class="fas fa-chart-line text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Revenue</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ ledger_stats.revenue_count }}</p>
        </div>
      </div>
    </div>

    <!-- Total Budgets -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#40657F]/5 to-[#2C3E50]/5 border-l-4 border-[#40657F]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#40657F] rounded-xl flex items-center justify-center">
          <i class="fas fa-calculator text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Budgets</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ budget_stats.total_budgets }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Ledgers Section -->
  <div class="card-modern p-8 ledgers-section-fade-in">
    <div class="flex flex-col md:flex-row items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-book text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Chart of Accounts
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if ledger_search %}
            Search results for "{{ ledger_search }}"
          {% else %}
            All ledger accounts in the system
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-book text-sm"></i>
          <span>{{ ledger_total_count }} account{{ ledger_total_count|pluralize }}</span>
        </div>
        <a
          href="{% url 'finances:bulk_delete_ledgers' %}"
          class="bg-[#F28C8C] text-white px-4 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm font-medium"
          title="Bulk Delete"
        >
          <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>

    <!-- Ledger Search -->
    <form method="GET" class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
          <input
            type="text"
            name="ledger_search"
            value="{{ ledger_search }}"
            placeholder="Search accounts..."
            class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
          />
          <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"></i>
        </div>
        <select
          name="ledger_type"
          class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
        >
          <option value="">All Types</option>
          <option value="Asset" {% if ledger_type_filter == 'Asset' %}selected{% endif %}>Asset</option>
          <option value="Liability" {% if ledger_type_filter == 'Liability' %}selected{% endif %}>Liability</option>
          <option value="Equity" {% if ledger_type_filter == 'Equity' %}selected{% endif %}>Equity</option>
          <option value="Revenue" {% if ledger_type_filter == 'Revenue' %}selected{% endif %}>Revenue</option>
          <option value="Expense" {% if ledger_type_filter == 'Expense' %}selected{% endif %}>Expense</option>
        </select>
        <div class="flex gap-2">
          <button
            type="submit"
            class="flex-1 bg-[#7AB2D3] text-white px-4 py-3 rounded-xl hover:bg-[#40657F] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if ledger_search or ledger_type_filter %}
          <a
            href="{% url 'finances:budget_ledger_management' %}"
            class="bg-[#B9D8EB] text-[#40657F] px-4 py-3 rounded-xl hover:bg-[#E2F1F9] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-times"></i>
          </a>
          {% endif %}
        </div>
      </div>
    </form>

    <!-- Ledgers Table -->
    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                Code
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-tag text-[#40657F]"></i>
                Account Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-layer-group text-[#74C69D]"></i>
                Type
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-dollar-sign text-[#F28C8C]"></i>
                Balance
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-cog text-[#7AB2D3]"></i>
                Actions
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for ledger in ledgers %}
          <tr
            class="ledger-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <span class="font-mono text-[#40657F] font-bold">{{ ledger.code }}</span>
            </td>
            <td class="px-6 py-4">
              <a
                href="{% url 'finances:ledger_details' ledger.pk %}"
                class="font-bold text-[#2C3E50] hover:text-[#7AB2D3] transition-colors duration-200"
              >
                {{ ledger.name }}
              </a>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold
                {% if ledger.ledger_type == 'Asset' %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30
                {% elif ledger.ledger_type == 'Revenue' %}bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30
                {% elif ledger.ledger_type == 'Expense' %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30
                {% else %}bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30{% endif %}"
              >
                {{ ledger.ledger_type }}
              </span>
            </td>
            <td class="px-6 py-4 text-right">
              <span class="font-mono text-[#2C3E50] font-bold">
                MWK {{ ledger.total_amount|intcomma }}
              </span>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="flex items-center justify-center gap-2">
                <a
                  href="{% url 'finances:ledger_details' ledger.pk %}"
                  class="bg-[#7AB2D3] text-white px-3 py-2 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm"
                  title="View Details"
                >
                  <i class="fas fa-eye"></i>
                </a>
                <a
                  href="{% url 'finances:edit_ledger' ledger.pk %}"
                  class="bg-[#74C69D] text-white px-3 py-2 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm"
                  title="Edit"
                >
                  <i class="fas fa-edit"></i>
                </a>
              </div>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="5" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-book text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Ledger Accounts Found
                  </h3>
                  <p class="text-[#40657F]">
                    {% if ledger_search %}
                      No accounts match your search criteria.
                    {% else %}
                      Get started by creating your first ledger account.
                    {% endif %}
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Ledger Pagination -->
    {% if ledgers.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-6">
      <div class="flex items-center gap-2">
        {% if ledgers.has_previous %}
        <a
          href="?ledger_page=1{% if ledger_search %}&ledger_search={{ ledger_search }}{% endif %}{% if ledger_type_filter %}&ledger_type={{ ledger_type_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?ledger_page={{ ledgers.previous_page_number }}{% if ledger_search %}&ledger_search={{ ledger_search }}{% endif %}{% if ledger_type_filter %}&ledger_type={{ ledger_type_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold">
          Page {{ ledgers.number }} of {{ ledgers.paginator.num_pages }}
        </span>

        {% if ledgers.has_next %}
        <a
          href="?ledger_page={{ ledgers.next_page_number }}{% if ledger_search %}&ledger_search={{ ledger_search }}{% endif %}{% if ledger_type_filter %}&ledger_type={{ ledger_type_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?ledger_page={{ ledgers.paginator.num_pages }}{% if ledger_search %}&ledger_search={{ ledger_search }}{% endif %}{% if ledger_type_filter %}&ledger_type={{ ledger_type_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Budgets Section -->
  <div class="card-modern p-8 budgets-section-fade-in">
    <div class="flex flex-col md:flex-row items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg budget-icon-float"
      >
        <i class="fas fa-calculator text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Budget Management
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if budget_search %}
            Search results for "{{ budget_search }}"
          {% else %}
            All budgets in the system
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-calculator text-sm"></i>
          <span>{{ budget_total_count }} budget{{ budget_total_count|pluralize }}</span>
        </div>
        <a
          href="{% url 'finances:bulk_delete_budgets' %}"
          class="bg-[#F28C8C] text-white px-4 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm font-medium"
          title="Bulk Delete"
        >
          <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>

    <!-- Budget Search -->
    <form method="GET" class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
          <input
            type="text"
            name="budget_search"
            value="{{ budget_search }}"
            placeholder="Search budgets..."
            class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
          />
          <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"></i>
        </div>
        <select
          name="term"
          class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
        >
          <option value="">All Terms</option>
          {% for term in budget_search_form.term.field.queryset %}
          <option value="{{ term.pk }}" {% if term.pk|stringformat:"s" == term_filter %}selected{% endif %}>
            {{ term.term_name }} - {{ term.academic_year.name }}
          </option>
          {% endfor %}
        </select>
        <div class="flex gap-2">
          <button
            type="submit"
            class="flex-1 bg-[#74C69D] text-white px-4 py-3 rounded-xl hover:bg-[#5fb085] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if budget_search or term_filter %}
          <a
            href="{% url 'finances:budget_ledger_management' %}"
            class="bg-[#B9D8EB] text-[#40657F] px-4 py-3 rounded-xl hover:bg-[#E2F1F9] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-times"></i>
          </a>
          {% endif %}
        </div>
      </div>
    </form>

    <!-- Budgets Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% for budget in budgets %}
      <div
        class="budget-card bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 border-[#B9D8EB] hover:border-[#74C69D] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 p-6 rounded-2xl relative overflow-hidden group"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
        ></div>

        <!-- Budget Header -->
        <div class="relative z-10 mb-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-lg font-bold text-[#2C3E50]">
              {{ budget.term.term_name }}
            </h4>
            <span class="text-xs bg-[#74C69D]/20 text-[#74C69D] px-2 py-1 rounded-full font-medium">
              {{ budget.term.academic_year.name }}
            </span>
          </div>
          <p class="text-[#40657F] text-sm">
            {{ budget.term.start_date }} - {{ budget.term.end_date }}
          </p>
        </div>

        <!-- Budget Details -->
        <div class="relative z-10 space-y-3 mb-4">
          {% if budget.description %}
          <p class="text-sm text-[#40657F] line-clamp-2">
            {{ budget.description }}
          </p>
          {% endif %}

          <div class="flex items-center gap-2">
            <i class="fas fa-list text-[#74C69D] text-sm"></i>
            <span class="text-sm text-[#40657F]">
              {{ budget.budget_lines.count }} budget line{{ budget.budget_lines.count|pluralize }}
            </span>
          </div>
        </div>

        <!-- Budget Actions -->
        <div class="relative z-10 flex gap-2">
          <a
            href="{% url 'finances:budget_details' budget.pk %}"
            class="flex-1 bg-[#74C69D] text-white text-center py-2 px-3 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm font-medium"
          >
            <i class="fas fa-eye mr-1"></i>
            View Details
          </a>
          <a
            href="{% url 'finances:edit_budget' budget.pk %}"
            class="bg-[#7AB2D3] text-white py-2 px-3 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm font-medium"
            title="Edit Budget"
          >
            <i class="fas fa-edit"></i>
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      {% empty %}
      <div class="col-span-full text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-calculator text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Budgets Found
            </h3>
            <p class="text-[#40657F]">
              {% if budget_search %}
                No budgets match your search criteria.
              {% else %}
                Get started by creating your first budget.
              {% endif %}
            </p>
          </div>
          <a
            href="{% url 'finances:add_budget' %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-plus"></i>
            <span>Add First Budget</span>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Budget Pagination -->
    {% if budgets.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-8">
      <div class="flex items-center gap-2">
        {% if budgets.has_previous %}
        <a
          href="?budget_page=1{% if budget_search %}&budget_search={{ budget_search }}{% endif %}{% if term_filter %}&term={{ term_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#74C69D] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?budget_page={{ budgets.previous_page_number }}{% if budget_search %}&budget_search={{ budget_search }}{% endif %}{% if term_filter %}&term={{ term_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#74C69D] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#74C69D] text-white rounded-lg font-semibold">
          Page {{ budgets.number }} of {{ budgets.paginator.num_pages }}
        </span>

        {% if budgets.has_next %}
        <a
          href="?budget_page={{ budgets.next_page_number }}{% if budget_search %}&budget_search={{ budget_search }}{% endif %}{% if term_filter %}&term={{ term_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#74C69D] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?budget_page={{ budgets.paginator.num_pages }}{% if budget_search %}&budget_search={{ budget_search }}{% endif %}{% if term_filter %}&term={{ term_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#74C69D] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Stats Cards Animation */
  .stats-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statsCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Ledgers Section Animation */
  .ledgers-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: ledgersSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .ledger-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: ledgerRowSlideIn 0.4s ease-out forwards;
  }

  .ledger-row:nth-child(1) { animation-delay: 1.6s; }
  .ledger-row:nth-child(2) { animation-delay: 1.7s; }
  .ledger-row:nth-child(3) { animation-delay: 1.8s; }
  .ledger-row:nth-child(4) { animation-delay: 1.9s; }
  .ledger-row:nth-child(5) { animation-delay: 2s; }

  /* Budgets Section Animation */
  .budgets-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: budgetsSectionFadeIn 0.8s ease-out 2.2s forwards;
  }

  .budget-icon-float {
    animation: budgetIconFloat 4s ease-in-out infinite;
  }

  .budget-card {
    opacity: 0;
    transform: translateY(20px);
    animation: budgetCardSlideIn 0.4s ease-out forwards;
  }

  .budget-card:nth-child(1) { animation-delay: 2.4s; }
  .budget-card:nth-child(2) { animation-delay: 2.5s; }
  .budget-card:nth-child(3) { animation-delay: 2.6s; }
  .budget-card:nth-child(4) { animation-delay: 2.7s; }
  .budget-card:nth-child(5) { animation-delay: 2.8s; }
  .budget-card:nth-child(6) { animation-delay: 2.9s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes statsCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes ledgersSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes ledgerRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes budgetsSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes budgetIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes budgetCardSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .ledger-row, .budget-card {
      animation-delay: 1.2s;
    }

    .ledger-row:nth-child(n), .budget-card:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--item-index, 1));
    }
  }
</style>

{% endblock %}
